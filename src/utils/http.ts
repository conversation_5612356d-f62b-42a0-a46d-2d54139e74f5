// utils/http.ts
const BASE_URL = import.meta.env.VITE_API_BASE

// 类型定义
export interface RequestOptions {
  baseURL?: string
  timeout?: number
  header?: Record<string, string>
}

export interface HttpRequest {
  get: (url: string, params?: any, cache?: boolean, headers?: Record<string, string>) => Promise<any>
  post: (url: string, data?: any, headers?: Record<string, string>) => Promise<any>
  put: (url: string, data?: any, headers?: Record<string, string>) => Promise<any>
  del: (url: string, data?: any, headers?: Record<string, string>) => Promise<any>
  streamRequest: (url: string, method: string, data?: any, onChunk?: (chunk: any) => void, onComplete?: (data: any) => void, headers?: Record<string, string>) => Promise<void>
}

// 通用请求方法封装
function request(url: string, method: string, data: any = {}, cache = false, headers: Record<string, string> = {}) {
  return new Promise((resolve, reject) => {
    const wxAuth = uni.getStorageSync('wxAuth') || ''
    const token = uni.getStorageSync('wxAuth') || ''

    // 缓存处理
    const cacheKey = `${method}:${url}:${JSON.stringify(data)}`
    if (cache) {
      const cachedData = uni.getStorageSync(cacheKey)
      if (cachedData) {
        // 在非生产环境下输出缓存信息
        // console.log('返回缓存数据：', cacheKey)
        return resolve(cachedData)
      }
    }

    uni.request({
      url: BASE_URL + url,
      method: method as any,
      // #ifdef web || h5
      withCredentials: true,
      // #endif
      data,
      header: {
        'Content-Type': 'application/json',
        'Authorization': token.replace('sb-ctutangg91hkparu99tg-auth-token=base64-', 'Bearer '),
        'wx-auth': wxAuth,
        ...headers,

      },
      success: (res) => {
        if (res.statusCode === 200) {
          if (cache) {
            uni.setStorageSync(cacheKey, res.data)
          }
          resolve(res.data)
        }
        else if (res.statusCode === 401) {
          uni.showToast({ title: '请重新登录', icon: 'none' })
          uni.reLaunch({ url: '/pages/auth/login' })
          reject(new Error('未登录'))
        }
        else if (res.statusCode === 500) {
          uni.showToast({ title: res.data.message, icon: 'none' })
          reject(new Error(res.data.message))
        }
        else {
          const errorMsg = typeof res.data === 'object' && res.data !== null && 'msg' in res.data ? res.data.msg : '请求失败'
          uni.showToast({ title: errorMsg, icon: 'none' })
          reject(new Error(errorMsg))
        }
      },
      fail: (err) => {
        uni.showToast({ title: '网络错误', icon: 'none' })
        reject(err)
      },
    })
  })
}

/**
 * 流式请求方法 - 用于AI对话等需要流式响应的场景
 * @param url 请求地址
 * @param method 请求方法
 * @param headers 请求头
 * @param data 请求数据
 * @param onChunk 数据块回调函数
 * @param onComplete 请求完成回调函数

 * @returns Promise
 */
function streamRequest(urls: string, method: string, headers: Record<string, string> = {}, data: any = {}, onChunk?: (chunk: any) => void, onComplete?: (data: any) => void) {
  const wxAuth = uni.getStorageSync('wxAuth') || ''
  const token = uni.getStorageSync('token') || ''

  // 构建请求头
  const requestHeader = {
    'Authorization': token,
    'wx-auth': wxAuth,
    ...headers,
  }
  // 使用 uni.request 发起请求
  const requestTask = uni.request({
    url: BASE_URL + urls,
    method: method as any,
    timeout: 120000,
    data: method !== 'GET' ? data : {},
    header: requestHeader,
    // 响应类型设置为文本，便于处理流式数据
    responseType: 'arraybuffer',
    // 启用分块接收，实现真正的流式处理
    enableChunked: true,
    success: (res) => {
      console.log(res, 'success')
      if (onChunk) { onChunk(res) }

      // 处理成功响应
      if (res.statusCode === 200) {
        // 处理最后可能剩余的数据

        // 调用完成回调
      }
      else if (res.statusCode === 401) {
        uni.showToast({ title: '请重新登录', icon: 'none' })
        uni.reLaunch({ url: '/pages/auth/login' })
      }
      else {
        // 处理其他错误状态码
        const errorMsg = typeof res.data === 'object' && res.data !== null && 'msg' in res.data ? res.data.msg : '请求失败'
        uni.showToast({ title: errorMsg, icon: 'none' })
      }
    },
    complete: (res) => {
      console.log(res, 'complete')

      if (onComplete) { onComplete(res) }
    },
    fail: (res) => {
      console.log(res, 'fail')
      // if (onFail) { onFail(res) }
      uni.showToast({ title: '网络错误', icon: 'none' })
    },
  })

  // 返回请求任务，可用于中断请求
  return requestTask
}

/**
 * 创建配置化的请求实例
 * @param options 配置选项
 * @returns 请求实例
 */
export function createRequest(options: RequestOptions = {}): HttpRequest {
  // 使用配置的baseURL或默认值
  // 注意: 当前实现中没有使用该变量，如果需要使用请修改下面的代码
  // const baseURL = options.baseURL || BASE_URL
  const defaultHeaders = options.header || {}

  // 创建自定义请求函数
  const customRequest = (url: string, method: string, data: any = {}, cache = false, headers: Record<string, string> = {}) => {
    return request(url, method, data, cache, { ...defaultHeaders, ...headers })
  }

  // 创建自定义流式请求函数
  const customStreamRequest = (url: string, method: string, data: any = {}, onChunk?: (chunk: any) => void, onComplete?: (data: any) => void, headers: Record<string, string> = {}) => {
    return streamRequest(url, method, { ...defaultHeaders, ...headers }, data, onChunk, onComplete)
  }

  return {
    get: (url, params = {}, cache = false, headers = {}) => customRequest(url, 'GET', params, cache, headers),
    post: (url, data = {}, headers = {}) => customRequest(url, 'POST', data, false, headers),
    put: (url, data = {}, headers = {}) => customRequest(url, 'PUT', data, false, headers),
    del: (url, data = {}, headers = {}) => customRequest(url, 'DELETE', data, false, headers),
    streamRequest: customStreamRequest,
  }
}

// 导出 GET / POST / PUT / DELETE 方法
export const get = (url: string, params = {}, cache = false, headers = {}) => request(url, 'GET', params, cache, headers)
export const post = (url: string, data = {}, headers = {}) => request(url, 'POST', data, false, headers)
export const put = (url: string, data = {}, headers = {}) => request(url, 'PUT', data, false, headers)
export const del = (url: string, data = {}, headers = {}) => request(url, 'DELETE', data, false, headers)
export const uniRequest = request
// 导出流式请求方法
export { streamRequest }
